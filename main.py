import os
import re
import numpy as np
import matplotlib.pyplot as plt
from scipy.optimize import least_squares

# 配置matplotlib字体设置，避免中文字体警告
def configure_matplotlib_fonts():
    """配置matplotlib字体，优先使用系统可用的字体"""
    import matplotlib.font_manager as fm

    # 获取系统可用字体
    available_fonts = [f.name for f in fm.fontManager.ttflist]

    # 定义字体优先级列表（包含中文和英文字体）
    font_candidates = [
        'DejaVu Sans',      # 默认英文字体
        'Arial',            # 常见英文字体
        'Liberation Sans',  # Linux常见字体
        'SimHei',          # Windows中文字体
        'WenQuanYi Micro Hei',  # Linux中文字体
        'Arial Unicode MS', # Mac中文字体
        'Noto Sans CJK SC', # Google中文字体
    ]

    # 选择第一个可用的字体
    selected_font = 'DejaVu Sans'  # 默认字体
    for font in font_candidates:
        if font in available_fonts:
            selected_font = font
            break

    # 设置matplotlib参数
    plt.rcParams['font.sans-serif'] = [selected_font]
    plt.rcParams['axes.unicode_minus'] = False
    plt.rcParams['font.size'] = 10

    return selected_font

# 配置字体
selected_font = configure_matplotlib_fonts()
print(f"Using font: {selected_font}")

# 实际测试数据 (Id-Vg曲线，Vds=5V)
actual_test_data = {
    'v-sweep': np.array([0.0, 0.2, 0.4, 0.6, 0.8, 1.0, 1.2, 1.4, 1.6, 1.8, 2.0, 2.2, 2.4, 2.6, 2.8, 3.0, 3.2, 3.4, 3.6, 3.8, 4.0, 4.2, 4.4, 4.6, 4.8, 5.0]),
    'vids#branch': np.array([5.2e-11, 5.2e-11, 5.2e-11, 5.2e-11, 5.2e-11, 5.3e-11, 5.5e-11, 6.8e-11, 2.1e-10, 8.9e-10, 3.2e-08, 1.8e-06, 9.5e-05, 4.2e-03, 1.8e-01, 7.8, 28.5, 62.1, 108.3, 167.2, 238.5, 321.8, 416.9, 523.4, 640.8, 769.2]) * 1e2  # 转换为安培
}

def run_spice():
    os.system("ngspice ./ngspice_circuit/demo.cir -b -o ./ngspice_circuit/run_log.log")


def plot_data(data_dict, x_key, y_keys, title="Data Plot", output_filename=None):
    """
    使用matplotlib根据提取的数据字典绘制曲线。

    参数:
    - data_dict (dict): 由 extract_data_modified 函数返回的数据字典。
    - x_key (str): 作为 X 轴的列名 (例如 'v-sweep')。
    - y_keys (list of str): 一个或多个作为 Y 轴的列名列表 (例如 ['vds_fixed#branc'])。
    - title (str, optional): 图表的标题。
    - output_filename (str, optional): 如果提供，图表将保存到此文件，而不是显示出来。
                                        例如 'my_plot.png'。
    """
    # 1. 输入验证
    if not data_dict:
        print("数据字典为空，无法绘图。")
        return

    if x_key not in data_dict:
        print(f"错误：X轴的键 '{x_key}' 不在数据中。")
        return

    # 为了方便，即使用户只提供一个y_key字符串，也将其转换为列表
    if isinstance(y_keys, str):
        y_keys = [y_keys]

    # 2. 创建图表和坐标轴
    fig, ax = plt.subplots(figsize=(10, 6)) # figsize可以调整图表大小

    # 3. 提取 X 轴数据
    x_values = data_dict[x_key]

    # 4. 循环绘制所有 Y 轴曲线
    for y_key in y_keys:
        if y_key in data_dict:
            y_values = data_dict[y_key]
            # 确保X和Y数据点数量一致
            if len(x_values) == len(y_values):
                ax.plot(x_values, y_values, marker='.', linestyle='-', label=y_key)
            else:
                print(f"警告：X轴 '{x_key}' 和 Y轴 '{y_key}' 的数据点数量不匹配，已跳过。")
        else:
            print(f"警告：Y轴的键 '{y_key}' 不在数据中，已跳过。")

    # 5. 美化图表
    ax.set_title(title, fontsize=16)
    ax.set_xlabel(x_key, fontsize=12)
    ax.set_ylabel("Values", fontsize=12)
    ax.grid(True, linestyle='--', alpha=0.6) # 添加网格线
    ax.legend() # 显示图例（基于plot中的label）

    # 6. 显示或保存图表
    if output_filename:
        try:
            plt.savefig(output_filename, dpi=300, bbox_inches='tight')
            print(f"图表已成功保存到 '{output_filename}'")
        except Exception as e:
            print(f"保存文件时出错: {e}")
    else:
        plt.show()


def extract_data(filename):
    with open(filename, "r") as f:
        lines = f.readlines()

    dicts = {}
    
    # 1. 找到所有分隔符行的索引
    separator = "--------------------------------------------------------------------------------"
    separator_indices = [i for i, line in enumerate(lines) if separator in line]

    # 2. 确保找到了至少两个分隔符
    if len(separator_indices) < 2:
        print("错误：未能找到定义数据块的两个分隔符。")
        return dicts  # 返回空字典

    # 3. 确定表头和数据区域的位置
    header_pos = separator_indices[0]
    data_start_pos = separator_indices[1]

    # 4. 提取并初始化表头 (keys)
    # 表头在第一个分隔符的下一行
    key_str = lines[header_pos + 1]
    keys = key_str.strip().split()
    
    # 从第二个 key 开始（跳过 'Index'）
    for key in keys[1:]:
        dicts[key] = []

    # 5. 遍历并提取数据行
    # 数据从第二个分隔符的下一行开始
    for line in lines[data_start_pos + 1:]:
        # 如果是空行或格式不对，就停止
        if not line.strip():
            break

        try:
            values = line.strip().split()
            # 确保行中有足够的数据列
            if len(values) < len(keys):
                continue # 如果列数不够，跳过此行

            # 遍历表头（从第二个开始），填充数据
            # 这里修正了原始代码的索引错误
            for i, key in enumerate(keys[1:]):
                # `keys[1]` 对应 `values[1]`, `keys[2]` 对应 `values[2]`, ...
                # `enumerate(keys[1:])` 中 i 从 0 开始，所以 `values` 的索引是 `i + 1`
                dicts[key].append(float(values[i + 1]))
        
        except (ValueError, IndexError):
            # 如果某一行无法转换为数字 (ValueError)，
            # 或者索引超出范围 (IndexError)，
            # 说明已经读到文件末尾的非数据部分，直接停止循环。
            break
            
    return dicts


def update_spice_params(lib_file_path, **params):
    """
    更新SPICE库文件中的参数值
    
    参数:
    - lib_file_path (str): SPICE库文件路径
    - **params: 要更新的参数，格式为 param_name=value
    
    示例:
    update_spice_params("./ngspice_circuit/lib/POWERMOS.lib", 
                       param_MINT_Vto=2.5, 
                       param_DBD_Bv=45.0)
    """
    try:
        # 读取文件内容
        with open(lib_file_path, 'r') as f:
            content = f.read()
        
        # 更新每个参数
        for param_name, param_value in params.items():
            # 构建正则表达式模式，匹配 .PARAM param_name=旧值
            pattern = rf'(\.PARAM\s+{re.escape(param_name)}\s*=\s*)([^\s\n]+)'
            
            # 替换参数值
            replacement = rf'\g<1>{param_value}'
            content = re.sub(pattern, replacement, content)
        
        # 写回文件
        with open(lib_file_path, 'w') as f:
            f.write(content)
            
        print(f"成功更新了 {len(params)} 个参数")
        for param_name, param_value in params.items():
            print(f"  {param_name} = {param_value}")
            
    except FileNotFoundError:
        print(f"错误：找不到文件 {lib_file_path}")
    except Exception as e:
        print(f"更新参数时出错: {e}")


def fit_spice_params(initial_params, param_bounds=None, lib_file_path="./ngspice_circuit/lib/POWERMOS.lib",
                     actual_data=None, max_iterations=50, tolerance=1e-6):
    """
    使用最小二乘法拟合SPICE模型参数到实际测试数据

    参数:
    - initial_params (dict): 初始参数值，格式为 {'param_name': value}
    - param_bounds (dict, optional): 参数边界，格式为 {'param_name': (min_val, max_val)}
    - lib_file_path (str): SPICE库文件路径
    - actual_data (dict, optional): 实际测试数据，如果为None则使用全局的actual_test_data
    - max_iterations (int): 最大迭代次数
    - tolerance (float): 收敛容差

    返回:
    - fitted_params (dict): 拟合后的参数值
    - result_info (dict): 拟合结果信息（残差、迭代次数等）
    """

    if actual_data is None:
        actual_data = actual_test_data

    # 提取参数名称和初始值
    param_names = list(initial_params.keys())
    initial_values = [initial_params[name] for name in param_names]

    # 设置参数边界
    if param_bounds is not None:
        bounds = ([param_bounds[name][0] if name in param_bounds else -np.inf for name in param_names],
                 [param_bounds[name][1] if name in param_bounds else np.inf for name in param_names])
    else:
        bounds = (-np.inf, np.inf)

    def objective_function(params):
        """
        目标函数：计算仿真结果与实际数据的残差
        """
        try:
            # 构建参数字典
            param_dict = {param_names[i]: params[i] for i in range(len(param_names))}

            # 更新SPICE参数
            update_spice_params(lib_file_path, **param_dict)

            # 运行仿真
            run_spice()

            # 提取仿真数据
            sim_data = extract_data("./ngspice_circuit/run_log.log")

            if not sim_data:
                print("Warning: Failed to extract simulation data")
                return np.full(len(actual_data['vids#branch']), 1e6)  # 返回大的残差值

            # 获取仿真的电流数据（假设键名为第二个键）
            sim_keys = list(sim_data.keys())
            if len(sim_keys) < 2:
                print("Warning: Incorrect simulation data format")
                return np.full(len(actual_data['vids#branch']), 1e6)

            sim_current = np.array(sim_data[sim_keys[1]])  # 通常是电流数据
            actual_current = actual_data['vids#branch']

            # 确保数据长度一致
            min_len = min(len(sim_current), len(actual_current))
            sim_current = sim_current[:min_len]
            actual_current = actual_current[:min_len]

            
            # # 计算加权残差（分段处理不同电流范围）
            # residuals = np.zeros(min_len)
            
            # for i in range(min_len):
            #     actual_val = abs(actual_current[i])
            #     sim_val = abs(sim_current[i])
                
            # if actual_val < 1e-9:  # 亚阈值区域
            #     # 使用对数差异，但限制最大残差
            #     log_diff = np.log10(max(sim_val, 1e-15)) - np.log10(max(actual_val, 1e-15))
            #     residuals[i] = np.clip(log_diff, -5, 5) * 0.1  # 降低权重
            # elif actual_val < 1e-3:  # 中等电流区域
            #     # 相对误差
            #     residuals[i] = (sim_val - actual_val) / (actual_val + 1e-12) * 50
            # else:  # 高电流区域
            #     # 相对误差，但增加权重
            #     residuals[i] = (sim_val - actual_val) / (actual_val + 1e-12) * 1000.0

            # 计算相对残差（避免数值问题）
            # 使用对数尺度来处理大范围的电流值
            sim_log = np.log10(np.maximum(sim_current, 1e-15))  # 避免log(0)
            actual_log = np.log10(np.maximum(actual_current, 1e-15))

            residuals = sim_log - actual_log

            # residuals = sim_current - actual_current

            return residuals

        except Exception as e:
            print(f"Error in objective function calculation: {e}")
            return np.full(len(actual_data['vids#branch']), 1e6)  # 返回大的残差值

    print("Starting parameter fitting...")
    print(f"Initial parameters: {initial_params}")

    # 执行最小二乘优化
    try:
        result = least_squares(objective_function, 
                            initial_values, 
                            bounds=bounds,
                            max_nfev=max_iterations,
                            ftol=tolerance,
                            xtol=tolerance*1e-100,
                            gtol=tolerance,
                            # max_nfev=1000,
                            verbose=2,
                            method='trf')

        # 构建拟合后的参数字典
        fitted_params = {param_names[i]: result.x[i] for i in range(len(param_names))}

        # 计算最终残差
        final_residuals = objective_function(result.x)
        rms_error = np.sqrt(np.mean(final_residuals**2))

        result_info = {
            'success': result.success,
            'message': result.message,
            'iterations': result.nfev,
            'rms_error': rms_error,
            'final_cost': result.cost,
            'residuals': final_residuals
        }

        print("Parameter fitting completed!")
        print(f"Fitted parameters: {fitted_params}")
        print(f"RMS error: {rms_error:.6f}")
        print(f"Iterations: {result.nfev}")
        print(f"Convergence status: {result.success}")

        return fitted_params, result_info

    except Exception as e:
        print(f"Error during parameter fitting: {e}")
        return initial_params, {'success': False, 'message': str(e)}


def compare_simulation_with_actual(sim_data, actual_data=None, output_filename=None):
    """
    比较仿真数据与实际测试数据，并绘制对比图

    参数:
    - sim_data (dict): 仿真数据字典
    - actual_data (dict, optional): 实际测试数据，如果为None则使用全局的actual_test_data
    - output_filename (str, optional): 输出图片文件名
    """
    if actual_data is None:
        actual_data = actual_test_data

    if not sim_data:
        print("Error: Simulation data is empty")
        return

    # 创建图表
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

    # 提取仿真数据
    sim_keys = list(sim_data.keys())
    if len(sim_keys) >= 2:
        sim_voltage = np.array(sim_data[sim_keys[0]])
        sim_current = np.array(sim_data[sim_keys[1]])

        # 提取实际数据
        actual_voltage = actual_data['v-sweep']
        actual_current = actual_data['vids#branch']

        # 线性尺度对比图
        ax1.semilogy(sim_voltage, sim_current, 'b-', label='Simulation', linewidth=2)
        # ax1.plot(sim_voltage, sim_current, 'b-', label='Simulation', linewidth=2)
        ax1.plot(actual_voltage, actual_current, 'ro-', label='Measured Data', markersize=4)
        ax1.set_xlabel('Gate Voltage Vgs (V)')
        ax1.set_ylabel('Drain Current Id (A)')
        ax1.set_title('Id-Vg Characteristic Comparison (Linear Scale)')
        ax1.grid(True, alpha=0.3)
        ax1.legend()

        # 计算并显示误差
        # 插值仿真数据到实际数据的电压点
        if len(sim_voltage) > 1 and len(actual_voltage) > 1:
            sim_current_interp = np.interp(actual_voltage, sim_voltage, sim_current)

            # 计算相对误差
            relative_error = np.abs((sim_current_interp - actual_current) / (actual_current + 1e-15)) * 100

            ax2.plot(actual_voltage, relative_error, 'g-o', markersize=4)
            ax2.set_xlabel('Gate Voltage Vgs (V)')
            ax2.set_ylabel('Relative Error (%)')
            ax2.set_title('Fitting Relative Error')
            ax2.grid(True, alpha=0.3)

            # 计算平均误差
            mean_error = np.mean(relative_error)
            ax2.text(0.05, 0.95, f'Mean Relative Error: {mean_error:.2f}%',
                    transform=ax2.transAxes, verticalalignment='top',
                    bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

    plt.tight_layout()

    if output_filename:
        try:
            plt.savefig(output_filename, dpi=300, bbox_inches='tight')
            print(f"Comparison plot saved to '{output_filename}'")
        except Exception as e:
            print(f"Error saving comparison plot: {e}")
    else:
        plt.show()


def main():
    # 示例：使用参数拟合功能

    # 定义要拟合的初始参数
    initial_params = {
        'param_MINT_Vto': 2.5,      # 阈值电压
        'param_MINT_Kp': 90.0,      # 跨导参数
        'param_MINT_U0': 600.0,     # 载流子迁移率
        'param_MINT_Nfs': 440000000000.0,  # 有效栅源电容
        'param_MINT_Eta': 1000.0,   # 亚阈值摆幅
        # 'param_MINT_Level': 3.0,    # 模型级别
        'param_MINT_L': 1e-8,       # 门长度
        'param_MINT_W': 1e-8,       # 门宽度
        'pram_MINT_Gamma': 0.5,    # 栅长度调制
        'param_MINT_Phi': 0.6,      # 栅氧化层势垒高度
        'param_MINT_Is': 1e-24,     # 漏电流饱和电流
        'param_MINT_Js': 0.0,       # 漏电流表面浓度
        'param_MINT_Pb':0.8,        # 栅氧化层并联电阻
        'param_MINT_Tox': 5e-8,     # 栅氧化层厚度
        'param_MINT_Xj': 1e-8,     # 漏区掺杂深度
        'param_MINT_U0': 500.0,    # 初始载流子速度
        'param_MINT_Vmax': 1000.0  # 最大电压
    }

    # 定义参数边界（可选）
    param_bounds = {
        'param_MINT_Vto': (1.0, 4.0),     # 阈值电压范围
        'param_MINT_Kp': (10.0, 1500.0),  # 跨导参数范围
        'param_MINT_U0': (40.0, 8000.0),  # 载流子迁移率范围
        'param_MINT_Nfs': (1e5, 1e12),    # 有效栅源电容范围
        'param_MINT_Eta': (500.0, 2000.0),  # 亚阈值摆幅范围
        # 'param_MINT_Level': (2.0, 4.0),    # 模型级别范围
        'param_MINT_L': (1e-8, 1e8),       # 门长度范围
        'param_MINT_W': (1e-8, 1e8),       # 门宽度范围
        'pram_MINT_Gamma': (0.0, 100),     # 栅长度调制范围
        'param_MINT_Phi': (0.1, 3.0),      # 栅氧化层势垒高度范围
        'param_MINT_Is': (1e-25, 1e-6),    # 漏电流饱和电流范围
        'param_MINT_Js': (0.0, 1e-4),      # 漏电流表面浓度范围
        'param_MINT_Pb': (0.1, 3.0),        # 栅氧化层并联电阻范围
        'param_MINT_Tox': (1e-9, 1e-6),     # 栅氧化层厚度范围
        'param_MINT_Xj': (1e-10, 1e-7),      # 漏区掺杂深度范围
        'param_MINT_U0': (100.0, 1000.0),    # 初始载流子速度范围
        'param_MINT_Vmax': (1e2, 1e10)  # 最大电压范围
    }

    # 执行参数拟合
    fitted_params, fit_info = fit_spice_params(
        initial_params=initial_params,
        param_bounds=param_bounds,
        max_iterations=1000,
        tolerance=1e-15
    )

    # 使用拟合后的参数运行最终仿真
    print("\nRunning final simulation with fitted parameters...")
    update_spice_params("./ngspice_circuit/lib/POWERMOS.lib", **fitted_params)
    run_spice()
    dicts = extract_data("./ngspice_circuit/run_log.log")

    # 绘制拟合结果对比图
    compare_simulation_with_actual(dicts, output_filename="mosfet_fitting_comparison.png")

    # 可选：也可以运行原始的示例代码
    # update_spice_params("./ngspice_circuit/lib/POWERMOS.lib",
    #                    param_MINT_Vto=2.8,
    #                    param_DBD_Bv=50.0,
    #                    param_DBGS_Bv=40.0)
    #
    # run_spice()
    # dicts = extract_data("./ngspice_circuit/run_log.log")
    # keys = list(dicts.keys())
    # plot_data(dicts, keys[0], keys[1], title="MOSFET Transfer Characteristic (Vds = 5V)",
    #           output_filename="mosfet_transfer_char.png")


if __name__ == "__main__":
    main()


